# 电机控制系统架构说明文档

## 系统概述

该项目是一个基于STM32F103系列微控制器的FOC（磁场定向控制）伺服电机控制系统，通过CAN总线进行通信。系统支持多种电机类型、多种反馈方式，并且实现了完整的三环控制（位置环、速度环、电流环）。

## 系统架构

系统架构可分为以下几个核心模块：

### 1. 主程序模块（main.c）

- 系统入口点，负责初始化各个硬件外设
- 运行主循环，调用状态机处理函数
- 协调各个功能模块的运行

### 2. 通信模块

#### 2.1 CAN通信（can.c, canopen.c）
- 实现CAN总线通信协议
- 支持CANopen协议栈
- 处理电机控制指令的接收和状态反馈

#### 2.2 串口通信（usart.c, modbus.c）
- 实现RS232/RS485串行通信
- 支持Modbus协议
- 用于参数配置和调试

### 3. 电机控制模块

#### 3.1 FOC控制算法（mcpwm.c）
- 实现磁场定向控制算法
- 电机矢量控制的核心逻辑
- 转换坐标系（Clarke变换、Park变换）

#### 3.2 低级驱动（low_level.c）
- 直接与硬件交互的底层代码
- PWM信号生成
- ADC采样与处理

### 4. 控制算法模块

#### 4.1 位置环控制（position_loop.c）
- 实现电机位置闭环控制
- 位置误差计算及PI控制
- 输出速度指令给速度环

#### 4.2 速度环控制（velocity_loop.c）
- 实现电机速度闭环控制
- 速度误差计算及PI控制
- 输出电流指令给电流环

#### 4.3 电流环控制（current_loop.c）
- 实现电机电流闭环控制
- 电流误差计算及PI控制
- 输出PWM驱动信号

### 5. 状态管理模块

#### 5.1 DS402状态机（ds402.c）
- 实现CANopen DS402设备规范的状态机
- 管理电机的不同工作状态
- 处理状态转换和命令执行

### 6. 配置与工具模块

#### 6.1 参数配置（parameter.c）
- 电机控制参数管理
- 参数存储和加载
- 参数动态调整

#### 6.2 字典对象（Dic.c）
- 实现CANopen对象字典
- 参数映射表
- 控制命令与状态反馈

#### 6.3 辅助函数（utils.c）
- 提供通用工具函数
- 数学计算
- 调试辅助功能

## 调用关系

1. **主程序调用流程**：
   - 主程序初始化各个硬件模块
   - 启动DS402状态机
   - 周期性处理通信和控制任务

2. **控制器三环结构**：
   - 位置环控制器输出给速度环
   - 速度环控制器输出给电流环
   - 电流环控制器输出PWM驱动信号

3. **通信与控制交互**：
   - CAN通信接收外部控制命令
   - 更新电机状态参数
   - 反馈电机运行状态

4. **参数与状态管理**：
   - 参数模块为各功能模块提供配置数据
   - 状态机管理电机运行状态
   - 错误处理和安全保护

## 主要功能特点

1. **多种电机反馈支持**：
   - 编码器反馈
   - 霍尔传感器
   - Tamagawa编码器

2. **多种控制模式**：
   - 位置控制模式
   - 速度控制模式
   - 转矩控制模式

3. **丰富的通信接口**：
   - CANopen通信
   - Modbus通信
   - 串口调试

4. **完善的保护机制**：
   - 过压保护
   - 过流保护
   - 过温保护

## 硬件接口

- PWM输出：驱动电机功率模块
- 反馈接口：获取电机位置和速度信息
- CAN总线：实现网络化控制
- 串口接口：参数配置和调试

## FOC与SVPWM原理解析

好的，这是一个非常核心的电机控制问题。简单来说，**FOC 是一种高级的控制策略（大脑），而 SVPWM 是执行这个策略的具体方法（手脚）**。

它们是电机控制流程中处于不同层级、但紧密协作的两个部分。

---

### 1. FOC (Field-Oriented Control) - 磁场定向控制

FOC 是一种**控制策略**或**算法框架**。

*   **目标**: 它的核心思想是，通过复杂的坐标变换，将三相交流电机（如 PMSM）的定子电流解耦成两个独立的直流分量：
    1.  **励磁分量 (Id)**: 控制转子的磁场强度。对于永磁同步电机，通常希望这个分量为零，以达到最高效率。
    2.  **转矩分量 (Iq)**: 直接控制电机的输出转矩。
*   **优点**: 通过像控制直流电机一样独立控制转矩和磁链，FOC 可以实现对交流电机平滑、精确、高效的控制，尤其是在低速和堵转时。
*   **流程**:
    1.  **测量**: 采集电机的三相电流 (Ia, Ib, Ic) 和转子位置（角度）。
    2.  **变换**:
        *   **Clarke 变换**: 将三相静止坐标系 (abc) 下的电流变换为两相静止坐标系 (αβ) 下的电流 (Iα, Iβ)。
        *   **Park 变换**: 利用转子角度，将两相静止坐标系 (αβ) 变换为与转子同步旋转的两相旋转坐标系 (dq) 下的电流 (Id, Iq)。
    3.  **控制**: 在 dq 坐标系下，使用两个独立的 PI 控制器，分别将测量得到的 Id, Iq 与目标值 (Id_ref, Iq_ref) 进行比较，计算出需要施加的电压 Vd 和 Vq。
    4.  **反变换**:
        *   **反 Park 变换**: 将控制电压 Vd, Vq 变换回两相静止坐标系 (αβ)，得到 Vα 和 Vβ。
*   **FOC 的输出**: FOC 算法的最终输出结果是两个电压分量：**Vα** 和 **Vβ**。这个结果告诉我们，为了产生期望的转矩，我们需要在定子上合成一个怎样的电压矢量。

---

### 2. SVPWM (Space Vector Pulse Width Modulation) - 空间矢量脉宽调制

SVPWM 是一种**PWM 调制技术**。

*   **目标**: 它的任务是接收 FOC 算法计算出的目标电压矢量 (Vα, Vβ)，并将其**转换**为三相逆变桥（驱动电机的6个开关管）在下一个 PWM 周期内的具体开关时间。
*   **优点**: 相比传统的 SPWM，SVPWM 对直流母线电压的利用率更高（可达15%的提升），产生的电流谐波更小，使得电机运行更平稳。
*   **输入**: FOC 计算出的目标电压矢量 (Vα, Vβ)。
*   **输出**: 三个 PWM 通道的占空比（或导通时间），用于控制三相逆-变桥的开关动作。

---

### 它们如何协同工作？

整个控制流程像一个流水线：

**`目标转速/位置` -> `位置/速度环` -> `目标Iq,Id` -> `FOC电流环` -> `目标Vα,Vβ` -> `SVPWM` -> `三相PWM信号` -> `逆变器` -> `电机`**

1.  **FOC (大脑)**: 根据外部指令（如目标速度）和反馈（实际电流、位置），计算出当前需要施加的电压矢量 (Vα, Vβ)。它决定了“**我们要去哪里**”。
2.  **SVPWM (手脚)**: 接收 FOC 给出的电压矢量 (Vα, Vβ)，并精确地计算出三路 PWM 的占空比，以在物理世界中合成这个电压矢量。它决定了“**我们具体怎么走**”。

### 在您的代码中：

*   **FOC 的实现**：您的代码中没有直接展示完整的 FOC 闭环计算（PI控制器部分可能在定时器中断处理函数中，这里没有显示）。但是，`update_motor` 函数中计算电机电气角度 `ph` 的过程，是 FOC 算法（特别是 Park/反Park 变换）的关键输入。
*   **SVPWM 的实现**：在 `mcpwm.c` 文件中，`queue_modulation_timings` 函数清晰地展示了 SVPWM 的作用：

    ````c
    // filepath: c:\Users\<USER>\Desktop\my_project\foc_can\Source_code\Core\Src\mcpwm.c
    // ...existing code...
    void queue_modulation_timings(Motor_t* motors, int mod_alpha, int mod_beta) {
        
        // 接收 FOC 计算出的 Vα(mod_alpha) 和 Vβ(mod_beta)
        // 调用 SVPWM 算法核心
        SVM(mod_alpha, mod_beta, &tA, &tB, &tC); 
        
        // 将计算出的三相导通时间转换为 PWM 占空比
        motors->PWM1_Duty = (tC * TIM_1_8_PERIOD_CLOCKS)/1000;
        motors->PWM2_Duty = (tB * TIM_1_8_PERIOD_CLOCKS)/1000;
        motors->PWM3_Duty = (tA * TIM_1_8_PERIOD_CLOCKS)/1000;
    }
    // ...existing code...
    ````

### 总结

| 特性 | FOC (磁场定向控制) | SVPWM (空间矢量脉宽调制) |
| :--- | :--- | :--- |
| **类别** | 高级**控制策略** / 算法框架 | 底层**调制技术** / 执行方法 |
| **目标** | 将交流电机解耦，实现转矩和磁链的独立控制 | 合成目标电压矢量，生成 PWM 信号 |
| **输入** | 目标电流(Iq, Id)、实际电流、转子角度 | 目标电压矢量 (Vα, Vβ) |
| **输出** | 目标电压矢量 (Vα, Vβ) | 三相 PWM 占空比 |
| **关系** | **FOC 的输出是 SVPWM 的输入** | **SVPWM 是实现 FOC 控制目标的执行者** |

好的，根据您提供的 `current_loop.c` 文件，我们可以非常清晰地看到该项目 FOC（磁场定向控制）核心电流环的实现方式。

这是一个经典的、使用**定点整数运算**实现的 FOC 电流环。整个过程在 `Current_loop` 函数中完成，其执行流程严格遵循 FOC 理论：

---

### FOC 实现步骤详解：

#### 1. 测量 (Measurement)
首先，代码从 ADC 获取两相电流，并根据基尔霍夫定律计算出第三相电流。
*   `motors->PhaseU_current = phase_current_from_adcval(ADCValue[0]-ADC_Offset[0]);`
*   `motors->PhaseV_current = phase_current_from_adcval(ADCValue[1]-ADC_Offset[1]);`
*   `motors->PhaseW_current = -motors->PhaseU_current-motors->PhaseV_current;`

这是 FOC 的第一步：获取电机三相定子电流 `Ia`, `Ib`, `Ic` 的瞬时值。

#### 2. Clarke 变换 (Stationary Frame Transform)
接下来，代码将三相静止坐标系 (abc) 下的电流变换为两相正交静止坐标系 (αβ) 下的电流 `Iα` 和 `Iβ`。
*   `Ialpha = motors->PhaseU_current;`
*   `Ibeta = (1000 * (motors->PhaseV_current - motors->PhaseW_current))/1732;`

这里使用了简化的 Clarke 变换公式，并且用整数 `1732` 和 `1000` 来近似 `sqrt(3)`，这是定点运算的典型特征。

#### 3. Park 变换 (Rotating Frame Transform)
然后，代码利用电机转子的实时角度 `motors->phase`，将两相静止坐标系 (αβ) 变换为与转子同步旋转的两相旋转坐标系 (dq)，得到实际的励磁电流 `Id` 和转矩电流 `Iq`。
*   `int c = arm_cos_f32(motors->phase);`
*   `int s = arm_sin_f32(motors->phase);`
*   `Id = (c*Ialpha + s*Ibeta)/16384;`
*   `Iq = (c*Ibeta - s*Ialpha)/16384;`

这里调用了 `arm_math` 库的三角函数，并且除以 `16384` (即 2^14) 是为了在定点数运算中进行缩放。

#### 4. PI 控制器 (PI Controllers)
在 dq 坐标系下，代码将实际测量并变换得到的 `Id` 和 `Iq` 与外部传入的目标值 `Id_des` 和 `Iq_des` 进行比较，计算出误差，然后通过两个独立的 PI 控制器计算出需要施加的控制电压 `Vd` 和 `Vq`。
*   **计算误差**:
    *   `Ierr_d = Id_des - Id;`
    *   `Ierr_q = Iq_des - Iq;`
*   **PI 计算**:
    *   `Vd = (V_current_control_integral_d/10 + (Ierr_d * kcp))/1000;`
    *   `Vq = (V_current_control_integral_q/10 + (Ierr_q * kcp))/1000;`
    这里 `kcp` 是比例增益，`kci` (在下面使用) 是积分增益。

#### 5. 抗积分饱和 (Anti-Windup)
为了防止 PI 控制器的积分项在输出饱和时过度累积，代码实现了一个简单的抗积分饱和逻辑：只有当输出电压 `mod_q` 和 `mod_d` 没有达到限幅值时，才累加积分项。
*   `if (mod_q > Vq_out_limit) { mod_q = Vq_out_limit; }`
*   `else { V_current_control_integral_q += Ierr_q * kci; }`
*   对 `mod_d` 也执行了同样的操作。

#### 6. 反 Park 变换 (Inverse Park Transform)
PI 控制器计算出的 `Vd` 和 `Vq` 需要被变换回两相静止坐标系 (αβ)，得到 `mod_alpha` 和 `mod_beta`，这代表了最终要合成的目标电压矢量。
*   `mod_alpha = (c*mod_d - s*mod_q)/16384;`
*   `mod_beta  = (c*mod_q + s*mod_d)/16384;`

#### 7. 空间矢量调制 (SVPWM)
最后，将 `mod_alpha` 和 `mod_beta` 作为输入，调用 SVPWM 模块来计算出三相 PWM 的占空比。
*   `queue_modulation_timings(motors, mod_alpha, mod_beta);`

#### 8. 更新 PWM 占空比
计算出的 PWM 占空比被加载到定时器 (`TIM1`) 的捕获/比较寄存器中，从而在下一个 PWM 周期驱动逆变器，产生期望的电压。
*   `motors->motor_timer->Instance->CCR1 = motors->PWM1_Duty;`
*   `motors->motor_timer->Instance->CCR2 = motors->PWM2_Duty;`
*   `motors->motor_timer->Instance->CCR3 = motors->PWM3_Duty;`

### 总结

该项目的 FOC 实现是一个完整且经典的教科书式案例。它在一个高频率执行的函数 (`Current_loop`，通常由定时器中断触发) 中，完成了从电流采样到 PWM 更新的整个闭环控制流程。其主要特点是**完全使用整数进行定点运算**，这在资源有限的微控制器（如 STM32F1）上可以获得比浮点运算更高的效率。

好的，这是一个关于 FOC 控制最核心的问题。Clarke 变换和 Park 变换是 FOC 算法的基石，它们协同工作，将复杂的交流控制问题简化为简单的直流控制问题。

---

### 1. Clarke 变换：化三为二

**核心作用：将三相静止坐标系 (abc) 下的交流信号，变换为两相正交静止坐标系 (αβ) 下的交流信号。**

*   **输入**: 电机实际测量的三相电流 `Ia`, `Ib`, `Ic`。这三个电流是随时间变化的、互差 120 度的正弦波。
*   **输出**: 两个正交的电流分量 `Iα` 和 `Iβ`。它们仍然是随时间变化的正弦波。

**为什么需要它？**

1.  **降维/简化**: 在一个平衡的三相系统中，任何时刻都有 `Ia + Ib + Ic = 0`。这意味着这三个变量不是独立的，知道其中两个就能算出第三个。Clarke 变换利用这个特性，将三个相关的变量降维成两个独立的变量，大大简化了后续的计算。
2.  **正交化**: 它将 120 度分布的三相系统，变成了一个标准的、互相垂直的二维直角坐标系 (αβ)，为下一步的旋转变换（Park 变换）做好了准备。

**一个比喻**: 想象一下，你有三个力 F_a, F_b, F_c，互成 120 度角拉一个物体。Clarke 变换就是帮你计算出，要达到同样的效果，你只需要在 X 方向施加一个力 F_x (`Iα`) 和在 Y 方向施加一个力 F_y (`Iβ`) 就行了。问题从三个方向的力简化成了两个正交方向的力。

---

### 2. Park 变换：变交流为直流

**核心作用：将两相静止坐标系 (αβ) 下的交流信号，变换为与转子同步旋转的两相旋转坐标系 (dq) 下的直流信号。**

*   **输入**: Clarke 变换得到的 `Iα`, `Iβ`，以及通过编码器获得的**电机转子的实时角度 `θ`**。
*   **输出**: 两个直流分量 `Id` 和 `Iq`。
    *   **`Id` (d-axis, direct axis)**: **励磁电流**。它与转子的磁场方向对齐。控制它就是控制转子的磁场强度。对于永磁同步电机，我们通常希望 `Id` 为 0，因为转子自己有永磁体，不需要额外的励磁电流。
    *   **`Iq` (q-axis, quadrature axis)**: **转矩电流**。它与转子磁场方向垂直。**这个分量的大小与电机的输出转矩成正比**。

**为什么需要它？**

这是 FOC 的**“魔术”**所在。

1.  **解耦控制**: 它成功地将交流电机的定子电流分解成了两个独立的直流分量：一个管磁场 (`Id`)，一个管转矩 (`Iq`)。这就好像把一个复杂的交流电机，变成了可以独立控制磁场和转矩的直流电机一样。
2.  **简化控制**: 控制一个随时间快速变化的交流信号是非常困难的，而控制一个直流信号则非常简单。我们只需要使用经典的 **PI 控制器**，就能非常精确地让实际的 `Id` 和 `Iq` 跟随我们的目标值，从而实现对转矩的精确控制。

**一个比喻**: 想象你在地面上（静止坐标系 αβ）观察一个旋转木马。你看到的马儿在做上下前后复杂的周期运动（交流信号）。现在你跳上旋转木马，和它一起旋转（同步旋转坐标系 dq）。这时，你正前方的马儿相对于你来说，是静止不动的（直流信号）。Park 变换就相当于帮你完成了“跳上旋转木马”这个动作，让你能用最简单的方式去观察和控制马儿。

### 总结

| 变换 | 作用 | 输入 | 输出 | 目的 |
| :--- | :--- | :--- | :--- | :--- |
| **Clarke** | **化三为二** | 三相交流电流 (Ia, Ib, Ic) | 两相交流电流 (Iα, Iβ) | **降维**，为 Park 变换做准备 |
| **Park** | **变交流为直流** | 两相交流电流 (Iα, Iβ) + 转子角度 (θ) | 两相直流分量 (Id, Iq) | **解耦**，实现对转矩和磁链的独立、精确控制 |

在您的项目中，这两个变换紧密相连，在 `current_loop.c` 中被依次执行，构成了 FOC 电流环路的核心。

好的，这是一个非常核心的概念。在 FOC（磁场定向控制）和 SVPWM 的上下文中，`alpha` (α) 和 `beta` (β) 分量代表了**目标电压矢量在两相静止坐标系 (α-β Frame) 下的 X 和 Y 坐标**。

让我们来详细分解一下：

### 1. 简单理解：一个二维坐标
您可以将 `alpha` 和 `beta` 简单地理解为：
*   **`alpha`**: 目标电压矢量在 **X 轴**上的分量。
*   **`beta`**: 目标电压矢量在 **Y 轴**上的分量。

这两个分量共同定义了一个矢量，这个矢量有明确的**大小**（`sqrt(alpha² + beta²) `）和**方向**（`atan2(beta, alpha)`）。这个矢量就是我们希望在下一个 PWM 周期内，通过三相逆变器在电机定子上合成的等效电压。

### 2. 物理意义：定子磁场的“指令”
在物理上，这个由 `alpha` 和 `beta` 构成的电压矢量，其最终目的是在电机的定子绕组中产生一个特定方向和强度的**合成磁场**。通过连续不断地改变 `alpha` 和 `beta` 的值，我们就能在定子中创造出一个平滑旋转的磁场，从而拖动转子旋转。

*   **α-轴**: 在物理上，通常将 α-轴与电机三相绕组中的 **A 相（或 U 相）绕组的轴线对齐**。
*   **β-轴**: β-轴则与 α-轴正交（领先 90 度）。

所以，`alpha` 分量的大小可以粗略地理解为“需要在 A 相方向上施加多大的电压分量”，而 `beta` 分量则是“需要在与 A 相垂直的方向上施加多大的电压分量”。

### 3. 在控制流程中的位置
`alpha` 和 `beta` 是连接 FOC 算法和 SVPWM 模块的**桥梁**。

1.  **FOC 的输出**:
    *   FOC 的电流环经过一系列复杂的计算（Clarke 变换、Park 变换、PI 控制）后，最终确定了为了达到目标转矩所需要施加的控制电压 `Vd` 和 `Vq`（在旋转坐标系下）。
    *   然后，通过**反 Park 变换**，将 `Vd` 和 `Vq` 从旋转坐标系变换回静止坐标系，其输出**正是 `alpha` 和 `beta`**。
    *   `alpha = Vd * cos(θ) - Vq * sin(θ)`
    *   `beta = Vd * sin(θ) + Vq * cos(θ)`

2.  **SVPWM 的输入**:
    *   您在 `utils.c` 中看到的 `SVM` 函数，其输入参数 `int alpha` 和 `int beta` 就是 FOC 计算出的结果。
    *   `SVM` 函数的任务就是接收这个“指令”（`alpha`, `beta`），并将其翻译成三相逆变器能听懂的语言——即三路 PWM 的具体占空比。

### 总结

| 分量 | 简单理解 | 物理意义 | 在流程中的角色 |
| :--- | :--- | :--- | :--- |
| **`alpha`** | 目标电压的 **X 坐标** | 沿电机 A 相轴线方向的电压分量 | FOC 的输出，SVPWM 的输入 |
| **`beta`** | 目标电压的 **Y 坐标** | 沿与 A 相垂直方向的电压分量 | FOC 的输出，SVPWM 的输入 |

因此，`alpha` 和 `beta` 是 FOC 算法经过深思熟虑后，下达给底层 PWM 生成模块的一个精确指令，告诉它：“请在定子上合成这样一个方向和大小的电压矢量”。

我将为您介绍该项目中使用ADC采集电压和电流的实现流程。让我首先查看相关的ADC文件，了解项目中ADC的实现细节。 根据对代码的分析，我将为您详细介绍该项目中使用ADC采集电压和电流的实现流程：

# 项目ADC采集电压和电流的实现流程

## 1. 硬件配置

该项目使用STM32F1系列微控制器的两个ADC转换器（ADC1和ADC2）进行电压和电流的采集：

1. **ADC1配置**:
   - 常规转换通道: ADC_CHANNEL_0，连接到PA0-WKUP引脚（Temp_Pin），用于采集温度传感器电压
   - 注入转换通道: ADC_CHANNEL_8，连接到PB0引脚，用于采集U相电流

2. **ADC2配置**:
   - 常规转换通道: ADC_CHANNEL_1，连接到PA1引脚（VBUS_Pin），用于采集总线电压
   - 注入转换通道: ADC_CHANNEL_9，连接到PB1引脚，用于采集V相电流

## 2. 初始化过程

1. **ADC初始化**: 
   - 在`MX_ADC1_Init`和`MX_ADC2_Init`函数中完成ADC的基本配置
   - 设置连续转换模式、右对齐数据格式、触发源等参数

2. **ADC校准流程**:
   - 在`Calibrate_ADC_Offset`函数中进行电流采样的零点校准
   - 启用ADC并使能中断
   - 启动注入组转换
   - 使用TIM1的通道4作为注入组转换的触发源
   - 存储零点偏移值到`ADC_Offset`数组
   - 判断零点偏移是否在合理范围内（1800-2200），否则设置ADC错误标志

## 3. 采样触发机制

- **电流采样**：
  - 使用定时器TIM1的通道4（TIM_CHANNEL_4）作为ADC注入转换的触发源
  - 在每次PWM周期结束时，通过TIM1_CC4中断触发ADC采样
  - 外部触发源设置为`ADC_EXTERNALTRIGINJECCONV_T1_CC4`

## 4. 数据采集过程

在`TIM1_CC_IRQHandler`定时器中断处理函数中：

1. 读取ADC注入通道的值：
   ```c
   ADCValue[0] = HAL_ADCEx_InjectedGetValue(&hadc1, 1);  // 获取ADC1注入通道1的值（U相电流）
   ADCValue[1] = HAL_ADCEx_InjectedGetValue(&hadc2, 1);  // 获取ADC2注入通道1的值（V相电流）
   ```

2. 读取ADC常规通道的值：
   ```c
   ADCValue[2] = HAL_ADC_GetValue(&hadc1);  // 获取ADC1常规通道的值（温度传感器）
   ADCValue[3] = HAL_ADC_GetValue(&hadc2);  // 获取ADC2常规通道的值（总线电压）
   ```

3. 计算总线电压：
   ```c
   vbus_voltage = (ADCValue[3] * 19) / 100;  // 计算总线电压
   ```

4. 计算温度传感器电阻和温度：
   ```c
   NTC_R_Value = 1000 * ADCValue[2] / (4096 - ADCValue[2]);  // 计算NTC电阻值
   device_temperature = Get_NTC_Temperature(NTC_R_Value);     // 根据NTC电阻获取温度
   ```

## 5. 电流转换处理

在`phase_current_from_adcval`函数中将ADC值转换为实际的电流值：

```c
int phase_current_from_adcval(uint32_t ADCValue) 
{
    int amp_gain = AMP_GAIN;
    int amp_out_volt = ONE_ADC_VOLTAGE * ADCValue;    // 计算ADC输出电压（μV）
    int shunt_volt = amp_out_volt / amp_gain;         // 计算分流器电压
    int current = (shunt_volt * 100) / motor.shunt_conductance;  // 计算电流（mA）
    return current;
}
```

## 6. 在FOC控制中的应用

在`Current_loop`函数中：

1. 获取校准后的相电流：
   ```c
   motors->PhaseU_current = phase_current_from_adcval(ADCValue[0] - ADC_Offset[0]);
   motors->PhaseV_current = phase_current_from_adcval(ADCValue[1] - ADC_Offset[1]);
   motors->PhaseW_current = -motors->PhaseU_current - motors->PhaseV_current;
   ```

2. 使用Clarke变换将三相电流转换为两相静止坐标系（α-β坐标系）：
   ```c
   Ialpha = motors->PhaseU_current;
   Ibeta = (1000 * (motors->PhaseV_current - motors->PhaseW_current)) / 1732;
   ```

3. 使用Park变换将静止坐标系的电流转换为旋转坐标系（d-q坐标系）：
   ```c
   Id = (c * Ialpha + s * Ibeta) / 16384;  // 直轴电流
   Iq = (c * Ibeta - s * Ialpha) / 16384;  // 交轴电流
   ```

## 7. 完整的采集流程

1. **初始化阶段**：
   - ADC硬件初始化
   - 电流传感器零点校准

2. **定时器中断触发**：
   - TIM1通道4触发ADC注入转换
   - 在TIM1_CC_IRQHandler中读取ADC值

3. **数据处理**：
   - 电压值计算（总线电压）
   - 电流值计算（相电流）
   - 温度值计算

4. **应用于控制算法**：
   - 电流值用于FOC（矢量控制）算法
   - 总线电压用于过压/欠压保护
   - 温度用于过热保护

该项目通过精确的ADC采集和校准，为电机的FOC控制提供了可靠的电流和电压输入，保证了控制算法的准确执行和系统的安全运行。


抱歉，我无法直接访问您提供的本地文件路径。不过，我可以基于已有的代码，分析电流采样电路的工作原理和参数计算方法。

让我分析代码中关于电流计算的相关部分： 根据代码分析，我可以为您解释`phase_current_from_adcval`函数中电流计算的原理和参数来源：

## 电流采样计算原理解析

从代码中的`phase_current_from_adcval`函数可以看出，电流采样计算过程分为以下几步：

```c
int phase_current_from_adcval(uint32_t ADCValue) 
{
    int amp_gain = AMP_GAIN;
    int amp_out_volt = ONE_ADC_VOLTAGE * ADCValue;
    int shunt_volt = amp_out_volt / amp_gain;
    int current = (shunt_volt*100) / motor.shunt_conductance; // unit mA
    return current;
}
```

### 1. 参数含义

1. **ADCValue**：ADC转换得到的原始数值（0-4095）
   
2. **AMP_GAIN**：放大器增益，定义为10.0（在mcpwm.h中）
   ```c
   #define AMP_GAIN 10.0
   ```

3. **ONE_ADC_VOLTAGE**：每一个ADC单位对应的电压值，单位是微伏(μV)
   ```c
   #define ONE_ADC_VOLTAGE 806  // unit uV
   ```
   
4. **shunt_conductance**：分流电阻的导电率，与电阻值成反比
   ```c
   .shunt_conductance = 300,  //100 means 1 mOh, current sensing resistor
   ```
   注释表明，该值为300表示电阻值为0.33毫欧姆(mΩ)（因为100代表1毫欧姆）

### 2. 计算过程分析

#### 步骤1：计算ADC对应的电压值
```c
int amp_out_volt = ONE_ADC_VOLTAGE * ADCValue;
```

这一步将ADC的原始读数转换为放大器输出端的电压值（单位：μV）。
- 每个ADC单位代表806μV
- 如果ADC读数为2048（满量程的一半），则对应电压为：806 * 2048 ≈ 1.65V

#### 步骤2：计算分流电阻上的电压降
```c
int shunt_volt = amp_out_volt / amp_gain;
```

由于运放电路对分流电阻上的电压进行了放大，所以需要除以放大倍数(AMP_GAIN=10)得到实际的分流电阻上的电压降（单位：μV）。

#### 步骤3：根据欧姆定律计算电流
```c
int current = (shunt_volt*100) / motor.shunt_conductance; // unit mA
```

根据欧姆定律 I = U/R，可以由分流电阻上的电压降和电阻值计算出通过电阻的电流。

- 这里使用了导电率(conductance)而不是电阻(resistance)
- 导电率 = 1/电阻
- motor.shunt_conductance = 300 表示电阻 = 1/(300/100) = 1/3 mΩ = 0.33 mΩ
- 乘以100是为了单位转换（μV转换为mA）

### 3. 参数计算原理

1. **ONE_ADC_VOLTAGE(806μV)**:
   - 这个值来自于ADC参考电压和ADC分辨率
   - 对于STM32F1系列，ADC参考电压通常为3.3V
   - 12位ADC分辨率意味着满量程为4096
   - 所以每个ADC单位代表的电压为: 3.3V / 4096 ≈ 0.806mV = 806μV

2. **AMP_GAIN(10.0)**:
   - 这是电流检测放大器的增益
   - 典型的电流检测放大器会将分流电阻上的小电压放大10倍或更多
   - 增益值10表示运放将分流电阻上的电压放大了10倍

3. **shunt_conductance(300)**:
   - 这是分流电阻的导电率，单位为"100表示1mΩ"
   - 值为300表示电阻值为0.33mΩ
   - 使用如此小的电阻是为了最小化电流检测对系统的影响

### 4. 实际电路工作原理

虽然我无法直接查看原理图，但根据代码可以推断电流采样电路的工作原理：

1. 电机的相电流通过一个非常小的分流电阻(0.33mΩ)
2. 分流电阻两端的电压差经过放大器放大10倍
3. 放大后的电压由ADC采样并转换为数字值
4. 软件通过上述计算将ADC值转换回原始电流值

### 具体计算例子：

假设ADC采样值为2048，让我们计算对应的电流：

1. 放大器输出电压 = 806 * 2048 = 1,650,688 μV ≈ 1.65 V
2. 分流电阻上的电压 = 1,650,688 / 10 = 165,068.8 μV
3. 电流 = (165,068.8 * 100) / 300 = 55,022.93 mA ≈ 55 A

这表明如果ADC值为2048（中点值），对应的电流约为55A，这意味着ADC可测量的电流范围约为±55A左右，这对于电机驱动器来说是合理的。

通过这种方式，系统可以精确测量电机的相电流，为FOC控制算法提供必要的反馈信息。