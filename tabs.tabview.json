{"type": "tabview", "vnumber": 100, "ctx": {"tabs": [{"name": "电机调试", "widgets": [{"path": "slider", "ctx": {".": {"ctx": {".": {"width": 271, "height": 100, "x": 0, "y": 518.7}, "resize_border": {"ctx": {".": {"status": {"mouse": {"mouseX": 812, "mouseY": 744}, "target": {"offset": {"x": 583.98, "y": 202}, "x": 0, "y": 505.40000000000003, "width": 271, "height": 100}, "types": [0]}, "full_screen": false, "full_height": false, "full_width": false}}}}, "from": 0, "to": 5000, "step_size": 1}, "slider_inside": {"target_value": 2158}, "argument_menu": {"ctx": {".": {"hex_on": false, "model_ctx": [{"float_value": 2158, "hex_value": "00 e0 06 45", "enabled": true}, {"float_value": 2158, "hex_value": "00 e0 06 45", "enabled": false}, {"float_value": 2158, "hex_value": "00 e0 06 45", "enabled": true}]}}}, "cmd_menu": {"ctx": [8]}, "ch_menu": {"ctx": -1}, "value_menu": {"ctx": {"attr": {"visible": true, "font_scale": 1, "color_": "black", "decimal": 0, "color_link_ch": true, "color_link_theme": false}}}, "name_menu": {"ctx": {"attr": {"visible": true, "font_scale": 1, "color_": "black", "color_link_ch": false, "color_link_theme": true, "name_": "name", "name_link_ch": false, "name_link_cmd": true}}}, "theme": {"ctx": {".": {"bgColorFollow": true, "bgColor": "#ffffff", "opacity": 1, "hideBorder": false}}}}}, {"path": "slider", "ctx": {".": {"ctx": {".": {"width": 271, "height": 100, "x": 292.6, "y": 13.3}, "resize_border": {"ctx": {".": {"status": {"mouse": {"mouseX": 878, "mouseY": 231}, "target": {"offset": {"x": 513.6699999999998, "y": 202}, "x": 305.90000000000003, "y": 13.3, "width": 271, "height": 100}, "types": [0]}, "full_status": {"mouse": {"mouseX": 0, "mouseY": 0}, "target": {"offset": {"x": 0, "y": 0}, "x": 305.90000000000003, "y": 13.3, "width": 271, "height": 100}, "types": []}, "full_screen": false, "full_height": false, "full_width": false}}}}, "from": 0, "to": 1000, "step_size": 1}, "slider_inside": {"target_value": 1}, "argument_menu": {"ctx": {".": {"hex_on": false, "model_ctx": [{"float_value": 350, "hex_value": "00 00 af 43", "enabled": true}, {"float_value": 350, "hex_value": "00 00 af 43", "enabled": false}, {"float_value": 350, "hex_value": "00 00 af 43", "enabled": true}]}}}, "cmd_menu": {"ctx": [2]}, "ch_menu": {"ctx": 1}, "value_menu": {"ctx": {"attr": {"visible": true, "font_scale": 1, "color_": "black", "decimal": 5, "color_link_ch": true, "color_link_theme": false}}}, "name_menu": {"ctx": {"attr": {"visible": true, "font_scale": 1, "color_": "black", "color_link_ch": false, "color_link_theme": true, "name_": "name", "name_link_ch": false, "name_link_cmd": true}}}, "theme": {"ctx": {".": {"bgColorFollow": true, "bgColor": "#ffffff", "opacity": 1, "hideBorder": false}}}}}, {"path": "slider", "ctx": {".": {"ctx": {".": {"width": 271, "height": 100, "x": 292.6, "y": 146.3}, "resize_border": {"ctx": {".": {"status": {"mouse": {"mouseX": 907, "mouseY": 334}, "target": {"offset": {"x": 513.6699999999998, "y": 202}, "x": 292.6, "y": 106.4, "width": 271, "height": 100}, "types": [0]}, "full_status": {"mouse": {"mouseX": 844, "mouseY": 801}, "target": {"offset": {"x": 513.98, "y": 202}, "x": 305.90000000000003, "y": 146.3, "width": 271, "height": 100}, "types": [0]}, "full_screen": false, "full_height": false, "full_width": false}}}}, "from": 0, "to": 1000, "step_size": 1}, "slider_inside": {"target_value": 2}, "argument_menu": {"ctx": {".": {"hex_on": false, "model_ctx": [{"float_value": 570, "hex_value": "00 80 0e 44", "enabled": true}, {"float_value": 570, "hex_value": "00 80 0e 44", "enabled": false}, {"float_value": 570, "hex_value": "00 80 0e 44", "enabled": true}]}}}, "cmd_menu": {"ctx": [4]}, "ch_menu": {"ctx": 4}, "value_menu": {"ctx": {"attr": {"visible": true, "font_scale": 1, "color_": "black", "decimal": 2, "color_link_ch": true, "color_link_theme": false}}}, "name_menu": {"ctx": {"attr": {"visible": true, "font_scale": 1, "color_": "black", "color_link_ch": false, "color_link_theme": true, "name_": "name", "name_link_ch": false, "name_link_cmd": true}}}, "theme": {"ctx": {".": {"bgColorFollow": true, "bgColor": "#ffffff", "opacity": 1, "hideBorder": false}}}}}, {"path": "slider", "ctx": {".": {"ctx": {".": {"width": 271, "height": 100, "x": 13.3, "y": 292.6}, "resize_border": {"ctx": {".": {"status": {"mouse": {"mouseX": 610, "mouseY": 519}, "target": {"offset": {"x": 513.6699999999998, "y": 202}, "x": 0, "y": 292.6, "width": 271, "height": 100}, "types": [0]}, "full_screen": false, "full_height": false, "full_width": false}}}}, "from": 0, "to": 1000, "step_size": 1}, "slider_inside": {"target_value": 2}, "argument_menu": {"ctx": {".": {"hex_on": false, "model_ctx": [{"float_value": 497, "hex_value": "00 80 f8 43", "enabled": true}, {"float_value": 497, "hex_value": "00 80 f8 43", "enabled": false}, {"float_value": 497, "hex_value": "00 80 f8 43", "enabled": true}]}}}, "cmd_menu": {"ctx": [5]}, "ch_menu": {"ctx": 4}, "value_menu": {"ctx": {"attr": {"visible": true, "font_scale": 1, "color_": "black", "decimal": 2, "color_link_ch": true, "color_link_theme": false}}}, "name_menu": {"ctx": {"attr": {"visible": true, "font_scale": 1, "color_": "black", "color_link_ch": false, "color_link_theme": true, "name_": "name", "name_link_ch": false, "name_link_cmd": true}}}, "theme": {"ctx": {".": {"bgColorFollow": true, "bgColor": "#ffffff", "opacity": 1, "hideBorder": false}}}}}, {"path": "slider", "ctx": {".": {"ctx": {".": {"width": 271, "height": 100, "x": 292.6, "y": 292.6}, "resize_border": {"ctx": {".": {"status": {"mouse": {"mouseX": 887, "mouseY": 522}, "target": {"offset": {"x": 513.6699999999998, "y": 202}, "x": 292.6, "y": 292.6, "width": 271, "height": 100}, "types": [0]}, "full_screen": false, "full_height": false, "full_width": false}}}}, "from": 0, "to": 1000, "step_size": 1}, "slider_inside": {"target_value": 0}, "argument_menu": {"ctx": {".": {"hex_on": false, "model_ctx": [{"float_value": 483, "hex_value": "00 80 f1 43", "enabled": true}, {"float_value": 483, "hex_value": "00 80 f1 43", "enabled": false}, {"float_value": 483, "hex_value": "00 80 f1 43", "enabled": true}]}}}, "cmd_menu": {"ctx": [6]}, "ch_menu": {"ctx": 5}, "value_menu": {"ctx": {"attr": {"visible": true, "font_scale": 1, "color_": "black", "decimal": 2, "color_link_ch": true, "color_link_theme": false}}}, "name_menu": {"ctx": {"attr": {"visible": true, "font_scale": 1, "color_": "black", "color_link_ch": false, "color_link_theme": true, "name_": "name", "name_link_ch": false, "name_link_cmd": true}}}, "theme": {"ctx": {".": {"bgColorFollow": true, "bgColor": "#ffffff", "opacity": 1, "hideBorder": false}}}}}, {"path": "slider", "ctx": {".": {"ctx": {".": {"width": 271, "height": 100, "x": 13.3, "y": 425.6}, "resize_border": {"ctx": {".": {"status": {"mouse": {"mouseX": 741, "mouseY": 620}, "target": {"offset": {"x": 521.98, "y": 202}, "x": 13.3, "y": 385.70000000000005, "width": 271, "height": 100}, "types": [0]}, "full_screen": false, "full_height": false, "full_width": false}}}}, "from": 0, "to": 1000, "step_size": 1}, "slider_inside": {"target_value": 499}, "argument_menu": {"ctx": {".": {"hex_on": false, "model_ctx": [{"float_value": 499, "hex_value": "00 80 f9 43", "enabled": true}, {"float_value": 499, "hex_value": "00 80 f9 43", "enabled": false}, {"float_value": 499, "hex_value": "00 80 f9 43", "enabled": true}]}}}, "cmd_menu": {"ctx": [7]}, "ch_menu": {"ctx": -1}, "value_menu": {"ctx": {"attr": {"visible": true, "font_scale": 1, "color_": "black", "decimal": 2, "color_link_ch": true, "color_link_theme": false}}}, "name_menu": {"ctx": {"attr": {"visible": true, "font_scale": 1, "color_": "black", "color_link_ch": false, "color_link_theme": true, "name_": "name", "name_link_ch": false, "name_link_cmd": true}}}, "theme": {"ctx": {".": {"bgColorFollow": true, "bgColor": "#ffffff", "opacity": 1, "hideBorder": false}}}}}, {"path": "<PERSON><PERSON>hart", "type": "inside", "ctx": {".": {"ctx": {".": {"width": 877.8000000000001, "height": 545.3000000000001, "x": 585.2, "y": 13.3}, "resize_border": {"ctx": {".": {"status": {"mouse": {"mouseX": 1609, "mouseY": 415}, "target": {"offset": {"x": 304.98, "y": 202}, "x": 585.2, "y": 13.3, "width": 718.2, "height": 545.3000000000001}, "types": [2]}, "full_screen": false, "full_height": false, "full_width": false}}}}, "font_size": 18, "xy_mode": false}, "axis_x_wave": {"ctx": {".": {"max_value": 49999, "min_value": 0, "left_index": 48973, "right_index": 49873.608856783496, "unit_text": "ms", "decimal": 0}, "container": {"ctx": []}}}, "axis_x_wave2": {"ctx": {".": {"max_value": 5, "min_value": -5, "left_index": 178.09449762326403, "right_index": -71.89816169102379, "unit_text": ""}, "container": {"ctx": []}}}, "axis_x_histog": {"ctx": {".": {"max_value": 5, "min_value": -5, "left_index": 0, "right_index": 10, "unit_text": "unit", "decimal": 3}, "container": {"ctx": []}}}, "axis_x_freq": {"ctx": {".": {"max_value": 5, "min_value": -5, "left_index": 0, "right_index": 257, "unit_text": "hz", "decimal": 3}, "container": {"ctx": []}}}, "axis_y_wave": {"ctx": {".": {"top_value": 60000.298828125, "bottom_value": -40000.19921875, "bar_index": 5, "decimal": 3}}}, "axis_y_bar": {"ctx": {".": {"top_value": 60000.298828125, "bottom_value": -40000.19921875, "bar_index": 5, "decimal": 3}}}, "wave_check": {"checked": true}, "bar_check": {"checked": false}, "freq_check": {"checked": false}, "histog_check": {"checked": false}, "simplify_check": {"checked": false}, "all_font_on": {"checked": false}, "hide_axis_x_check": {"checked": false}, "hide_axis_y_check": {"checked": false}, "hide_border_check": {"checked": false}, "auto_bt_menu": {"locked": false}, "rbw": {"ctx": {".": {"histog_gap_num": 10, "freq_gap_num": 257, "lines": [0], "fake_continuous": true, "line_width": 2}}}, "x_ch_menu": {"indecies": []}, "theme": {"ctx": {".": {"lineColor2": "#ff5500", "lineColor2Follow1": true, "lineColor2Follow2": false, "lineColor": "black", "lineColorFollow1": false, "lineColorFollow2": true}, "bgProperties": {"ctx": {".": {"bgType": 0, "bgOnTop": true, "bgOpacity": 1, "bgRatio": 1, "bgFillMode": 1, "bgLR": 0, "bgLRMargin": 0, "bgTB": 0, "bgTBMargin": 0, "bgSource": "", "bgColor": "#ffffff", "bgColorOpacity": 0.6, "bgSourceFollowed": false, "bgColorFollowed": true}}}}}}}, {"path": "slider", "ctx": {".": {"ctx": {".": {"width": 271, "height": 100, "x": 13.3, "y": 13.3}, "resize_border": {"ctx": {".": {"status": {"mouse": {"mouseX": 613, "mouseY": 274}, "target": {"offset": {"x": 357.98, "y": 183}, "x": 13.3, "y": 13.3, "width": 271, "height": 100}, "types": [0]}, "full_screen": false, "full_height": false, "full_width": false}}}}, "from": 0, "to": 1000, "step_size": 1}, "slider_inside": {"target_value": 50}, "argument_menu": {"ctx": {".": {"hex_on": false, "model_ctx": [{"float_value": 50, "hex_value": "00 00 48 42", "enabled": true}, {"float_value": 50, "hex_value": "00 00 48 42", "enabled": false}, {"float_value": 50, "hex_value": "00 00 48 42", "enabled": true}]}}}, "cmd_menu": {"ctx": [1]}, "ch_menu": {"ctx": 0}, "value_menu": {"ctx": {"attr": {"visible": true, "font_scale": 1, "color_": "black", "decimal": 2, "color_link_ch": true, "color_link_theme": false}}}, "name_menu": {"ctx": {"attr": {"visible": true, "font_scale": 1, "color_": "black", "color_link_ch": false, "color_link_theme": true, "name_": "name", "name_link_ch": false, "name_link_cmd": true}}}, "theme": {"ctx": {".": {"bgColorFollow": true, "bgColor": "#ffffff", "opacity": 1, "hideBorder": false}}}}}, {"path": "slider", "ctx": {".": {"ctx": {".": {"width": 271, "height": 100, "x": 13.3, "y": 146.3}, "resize_border": {"ctx": {".": {"status": {"mouse": {"mouseX": 528, "mouseY": 331}, "target": {"offset": {"x": 357.98, "y": 183}, "x": 13.3, "y": 146.3, "width": 271, "height": 100}, "types": [3]}, "full_screen": false, "full_height": false, "full_width": false}}}}, "from": 0, "to": 2000, "step_size": 1}, "slider_inside": {"target_value": 1300}, "argument_menu": {"ctx": {".": {"hex_on": false, "model_ctx": [{"float_value": 1756, "hex_value": "00 80 db 44", "enabled": true}, {"float_value": 1756, "hex_value": "00 80 db 44", "enabled": false}, {"float_value": 1756, "hex_value": "00 80 db 44", "enabled": true}]}}}, "cmd_menu": {"ctx": [3]}, "ch_menu": {"ctx": 2}, "value_menu": {"ctx": {"attr": {"visible": true, "font_scale": 1, "color_": "black", "decimal": 5, "color_link_ch": true, "color_link_theme": false}}}, "name_menu": {"ctx": {"attr": {"visible": true, "font_scale": 1, "color_": "black", "color_link_ch": false, "color_link_theme": true, "name_": "name", "name_link_ch": false, "name_link_cmd": true}}}, "theme": {"ctx": {".": {"bgColorFollow": true, "bgColor": "#ffffff", "opacity": 1, "hideBorder": false}}}}}, {"path": "slider", "ctx": {".": {"ctx": {".": {"width": 271, "height": 100, "x": 319.20000000000005, "y": 385.70000000000005}, "resize_border": {"ctx": {".": {"status": {"mouse": {"mouseX": 718, "mouseY": 670}, "target": {"offset": {"x": 357.98, "y": 183}, "x": 292.6, "y": 425.6, "width": 271, "height": 100}, "types": [0]}, "full_screen": false, "full_height": false, "full_width": false}}}}, "from": -20000, "to": 20000, "step_size": 1}, "slider_inside": {"target_value": 6857}, "argument_menu": {"ctx": {".": {"hex_on": false, "model_ctx": [{"float_value": 6857, "hex_value": "00 48 d6 45", "enabled": true}, {"float_value": 6857, "hex_value": "00 48 d6 45", "enabled": false}, {"float_value": 6857, "hex_value": "00 48 d6 45", "enabled": true}]}}}, "cmd_menu": {"ctx": [9]}, "ch_menu": {"ctx": -1}, "value_menu": {"ctx": {"attr": {"visible": true, "font_scale": 1, "color_": "black", "decimal": 0, "color_link_ch": true, "color_link_theme": false}}}, "name_menu": {"ctx": {"attr": {"visible": true, "font_scale": 1, "color_": "black", "color_link_ch": false, "color_link_theme": true, "name_": "name", "name_link_ch": false, "name_link_cmd": true}}}, "theme": {"ctx": {".": {"bgColorFollow": true, "bgColor": "#ffffff", "opacity": 1, "hideBorder": false}}}}}, {"path": "ExtraButtonToggle", "ctx": {".": {"ctx": {".": {"width": 106.4, "height": 106.4, "x": 319.20000000000005, "y": 505.40000000000003}, "resize_border": {"ctx": {".": {"status": {"mouse": {"mouseX": 772, "mouseY": 732}, "target": {"offset": {"x": 357.98, "y": 183}, "x": 319.20000000000005, "y": 452.20000000000005, "width": 106.4, "height": 106.4}, "types": [0]}, "full_screen": false, "full_height": false, "full_width": false}}}}, "reverse_logic": false, "threshold": 0}, "argument_menu": {"ctx": {".": {"hex_on": false, "model_ctx": [{"float_value": 1, "hex_value": "00 00 80 3f", "enabled": true}, {"float_value": 0, "hex_value": "00 00 00 00", "enabled": true}]}}}, "ch_menu": {"ctx": -1}, "cmd_menu": {"ctx": [0]}, "name_menu": {"ctx": {"attr": {"visible": true, "font_scale": 1, "color_": "black", "color_link_ch": false, "color_link_theme": true, "name_": "name", "name_link_ch": false, "name_link_cmd": true}}}, "theme": {"ctx": {".": {"colorBt": "#ffffff", "colorText": "#000000", "colorBorder": "#d0d0d0", "colorOn": "#1abc9c", "colorBtFollow": true, "colorTextFollow": false, "colorBorderFollow": true, "colorOnFollow": false, "colorOffFollow": true, "colorBtFollowCh": false, "colorTextFollowCh": true, "colorBorderFollowCh": false, "colorOnFollowCh": true, "colorOffFollowCh": false}}}}}], "file_url": "", "bg": {".": {"bgType": 1, "bgOnTop": false, "bgOpacity": 1, "bgRatio": 1, "bgFillMode": 1, "bgLR": 0, "bgLRMargin": 0, "bgTB": 0, "bgTBMargin": 0, "bgSource": "", "bgColor": "#ffffff", "bgColorOpacity": 1, "bgSourceFollowed": true, "bgColorFollowed": true}}}], "is_top": true, "x": 9, "y": 94, "width": 2485, "height": 1152, "currentIndex": 0, "lastIndex": 1, "file_url": "C:/Users/<USER>/Desktop/tabs.tabview.json"}}