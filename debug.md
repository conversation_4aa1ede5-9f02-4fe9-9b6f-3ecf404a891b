# 调试记录

## 问题：串口2无法接收上位机发送的数据

### 问题描述
在将串口2的发送方式改为DMA发送后，发现无法接收上位机发送的数据。

### 问题分析
通过分析代码，发现以下几个问题：

1. **串口2的接收模式混乱**：
   - 在`uart2_init`函数中，没有启动DMA接收，而是使用了中断接收
   - 但在`HAL_UART_TxCpltCallback`中又尝试使用DMA接收：`HAL_UART_Receive_DMA(&huart2, (uint8_t*)RS232_RX_BUFF, USART2_REC_LEN)`
   - 同时在`HAL_UART_RxCpltCallback`中又使用了中断接收：`HAL_UART_Receive_IT(&huart2, (uint8_t*)(RS232_RX_BUFF+RS232_RX_CNT), 1)`

2. **USART2中断处理冲突**：
   - 在`stm32f1xx_it.c`中的`USART2_IRQHandler`函数中直接处理了接收到的数据
   - 同时又调用了`HAL_UART_IRQHandler(&huart2)`，这可能导致重复处理

3. **DMA和中断接收混用**：
   - 系统同时使用了DMA接收和中断接收，这会导致冲突

### 解决方案

1. **统一使用DMA接收模式**：
   - 修改`uart2_init`函数，启动DMA接收
   ```c
   void uart2_init(u32 baudrate)
   {	
       //UART 初始化
       huart2.Instance=USART2;					    
       huart2.Init.BaudRate=baudrate;				    
       huart2.Init.WordLength=UART_WORDLENGTH_8B;   
       huart2.Init.StopBits=UART_STOPBITS_1;	    
       huart2.Init.Parity=UART_PARITY_NONE;		    
       huart2.Init.HwFlowCtl=UART_HWCONTROL_NONE;   
       huart2.Init.Mode=UART_MODE_TX_RX;		    
       HAL_UART_Init(&huart2);					   
       
       // 清空接收缓冲区
       memset(RS232_RX_BUFF, 0, USART2_REC_LEN);
       RS232_RX_CNT = 0;
       
       // 使能空闲中断，用于检测一帧数据接收完成
       __HAL_UART_ENABLE_IT(&huart2, UART_IT_IDLE);
       
       // 启动DMA接收
       HAL_UART_Receive_DMA(&huart2, (uint8_t*)RS232_RX_BUFF, USART2_REC_LEN);
   }
   ```

2. **正确处理空闲中断**：
   - 修改`USART2_IRQHandler`函数，处理DMA接收模式下的空闲中断
   ```c
   void USART2_IRQHandler(void)
   {
     HAL_UART_IRQHandler(&huart2);  // 调用UART2中断处理函数

     // 判断是不是串口2
     if(USART2 == huart2.Instance)                                   
     {
       // 判断是不是空闲中断
       if(RESET != __HAL_UART_GET_FLAG(&huart2, UART_FLAG_IDLE))   
       {	
         // 清除空闲中断标志
         __HAL_UART_CLEAR_IDLEFLAG(&huart2);  
             
         // 中止本次DMA传输
         HAL_UART_DMAStop(&huart2); 
                                                                  
         // 计算接收到的数据长度
         RS232_RX_CNT = USART2_REC_LEN - __HAL_DMA_GET_COUNTER(&hdma_usart2_rx);   
         
         if(RS232_RX_CNT > 0)
         {
           // 设置帧接收标志
           RS232_FrameFlag = 1;
           
           // 处理接收到的数据
           // 如果是VOFA命令，可以在这里处理
           for(uint16_t i = 0; i < RS232_RX_CNT; i++)
           {
             uartCMDRecv(RS232_RX_BUFF[i]);
             
             // 如果命令完成标志被置位，解析命令
             if(vofaCommandData.completionFlag)
             { 
               vofaCommandParse();
               vofaCommandData.completionFlag = 0;
             }
           }
           
           // 处理其他协议数据
           RS232_Solve_Service();
         }
         
         // 清零接收缓冲区
         memset(RS232_RX_BUFF, 0, RS232_RX_CNT); 
         RS232_RX_CNT = 0;
         
         // 重新启动DMA接收
         HAL_UART_Receive_DMA(&huart2, (uint8_t*)RS232_RX_BUFF, USART2_REC_LEN);
       }
     }
   }
   ```

3. **修改HAL_UART_TxCpltCallback函数**：
   - 删除对串口2的DMA接收启动，只保留RS232_TX_EN = 0
   ```c
   void HAL_UART_TxCpltCallback(UART_HandleTypeDef *huart)
   {
       if(huart->Instance==USART3)
       {
           Modbus_Solve_485_Disenable();	
           HAL_UART_Receive_DMA(&huart3, (uint8_t*)RS485_RX_BUFF, USART3_REC_LEN);  
       }		
       if(huart->Instance==USART2)
       {
           // 发送完成后将发送标志位清零
           RS232_TX_EN = 0;
       }		
   }
   ```

4. **修改RS232_Process函数**：
   - 因为使用DMA接收和空闲中断，不需要再在这个函数中处理超时
   ```c
   void RS232_Process(void)
   {
       // 使用DMA接收和空闲中断，不需要在这里处理超时
       // 空闲中断会自动处理帧接收完成
   }
   ```

### 注意事项
1. 确保上位机的串口设置（波特率、数据位、停止位、校验位）与单片机的设置一致
2. 使用DMA接收时，需要在空闲中断中处理接收到的数据
3. 避免同时使用DMA接收和中断接收，以防止冲突

### 结论
通过统一使用DMA接收模式，并正确处理空闲中断，解决了串口2无法接收上位机数据的问题。这种方式不仅提高了系统的响应性能，还减少了CPU的占用时间。
